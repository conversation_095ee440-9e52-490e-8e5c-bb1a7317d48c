// Code generated by go generate; DO NOT EDIT.
// Generated at: 2025-08-08T08:01:49+03:00

package models

// ModelInfo represents information about a specific model
type ModelInfo struct {
	ID          string
	Name        string
	Attachment  bool
	Reasoning   bool
	Temperature bool
	Cost        Cost
	Limit       Limit
}

// Cost represents the pricing information for a model
type Cost struct {
	Input      float64
	Output     float64
	CacheRead  *float64
	CacheWrite *float64
}

// Limit represents the context and output limits for a model
type Limit struct {
	Context int
	Output  int
}

// ProviderInfo represents information about a model provider
type ProviderInfo struct {
	ID     string
	Env    []string
	NPM    string
	Name   string
	Models map[string]ModelInfo
}

// GetModelsData returns the static models data from models.dev
func GetModelsData() map[string]ProviderInfo {
	return map[string]ProviderInfo{
		"alibaba": {
			ID:   "alibaba",
			Env:  []string{"DASHSCOPE_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Alibaba",
			Models: map[string]ModelInfo{
				"qwen3-coder-plus": {
					ID:          "qwen3-coder-plus",
					Name:        "Qwen3 Coder Plus",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
			},
		},
		"amazon-bedrock": {
			ID:   "amazon-bedrock",
			Env:  []string{"AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_REGION"},
			NPM:  "@ai-sdk/amazon-bedrock",
			Name: "Amazon Bedrock",
			Models: map[string]ModelInfo{
				"ai21.jamba-1-5-large-v1:0": {
					ID:          "ai21.jamba-1-5-large-v1:0",
					Name:        "Jamba 1.5 Large",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  4096,
					},
				},
				"ai21.jamba-1-5-mini-v1:0": {
					ID:          "ai21.jamba-1-5-mini-v1:0",
					Name:        "Jamba 1.5 Mini",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  4096,
					},
				},
				"amazon.nova-lite-v1:0": {
					ID:          "amazon.nova-lite-v1:0",
					Name:        "Nova Lite",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.06,
						Output:     0.24,
						CacheRead:  &[]float64{0.015}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 300000,
						Output:  8192,
					},
				},
				"amazon.nova-micro-v1:0": {
					ID:          "amazon.nova-micro-v1:0",
					Name:        "Nova Micro",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.035,
						Output:     0.14,
						CacheRead:  &[]float64{0.00875}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"amazon.nova-premier-v1:0": {
					ID:          "amazon.nova-premier-v1:0",
					Name:        "Nova Premier",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      2.5,
						Output:     12.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  16384,
					},
				},
				"amazon.nova-pro-v1:0": {
					ID:          "amazon.nova-pro-v1:0",
					Name:        "Nova Pro",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     3.2,
						CacheRead:  &[]float64{0.2}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 300000,
						Output:  8192,
					},
				},
				"anthropic.claude-3-5-haiku-20241022-v1:0": {
					ID:          "anthropic.claude-3-5-haiku-20241022-v1:0",
					Name:        "Claude Haiku 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     4,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic.claude-3-5-sonnet-20240620-v1:0": {
					ID:          "anthropic.claude-3-5-sonnet-20240620-v1:0",
					Name:        "Claude Sonnet 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic.claude-3-5-sonnet-20241022-v2:0": {
					ID:          "anthropic.claude-3-5-sonnet-20241022-v2:0",
					Name:        "Claude Sonnet 3.5 v2",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic.claude-3-7-sonnet-20250219-v1:0": {
					ID:          "anthropic.claude-3-7-sonnet-20250219-v1:0",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic.claude-3-haiku-20240307-v1:0": {
					ID:          "anthropic.claude-3-haiku-20240307-v1:0",
					Name:        "Claude Haiku 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     1.25,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"anthropic.claude-3-opus-20240229-v1:0": {
					ID:          "anthropic.claude-3-opus-20240229-v1:0",
					Name:        "Claude Opus 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"anthropic.claude-3-sonnet-20240229-v1:0": {
					ID:          "anthropic.claude-3-sonnet-20240229-v1:0",
					Name:        "Claude Sonnet 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"anthropic.claude-instant-v1": {
					ID:          "anthropic.claude-instant-v1",
					Name:        "Claude Instant",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     2.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 100000,
						Output:  4096,
					},
				},
				"anthropic.claude-opus-4-1-20250805-v1:0": {
					ID:          "anthropic.claude-opus-4-1-20250805-v1:0",
					Name:        "Claude Opus 4.1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"anthropic.claude-opus-4-20250514-v1:0": {
					ID:          "anthropic.claude-opus-4-20250514-v1:0",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"anthropic.claude-sonnet-4-20250514-v1:0": {
					ID:          "anthropic.claude-sonnet-4-20250514-v1:0",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"anthropic.claude-v2": {
					ID:          "anthropic.claude-v2",
					Name:        "Claude 2",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      8,
						Output:     24,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 100000,
						Output:  4096,
					},
				},
				"anthropic.claude-v2:1": {
					ID:          "anthropic.claude-v2:1",
					Name:        "Claude 2.1",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      8,
						Output:     24,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"cohere.command-light-text-v14": {
					ID:          "cohere.command-light-text-v14",
					Name:        "Command Light",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4096,
						Output:  4096,
					},
				},
				"cohere.command-r-plus-v1:0": {
					ID:          "cohere.command-r-plus-v1:0",
					Name:        "Command R+",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cohere.command-r-v1:0": {
					ID:          "cohere.command-r-v1:0",
					Name:        "Command R",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     1.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cohere.command-text-v14": {
					ID:          "cohere.command-text-v14",
					Name:        "Command",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4096,
						Output:  4096,
					},
				},
				"deepseek.r1-v1:0": {
					ID:          "deepseek.r1-v1:0",
					Name:        "DeepSeek-R1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.35,
						Output:     5.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"meta.llama3-1-70b-instruct-v1:0": {
					ID:          "meta.llama3-1-70b-instruct-v1:0",
					Name:        "Llama 3.1 70B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.72,
						Output:     0.72,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta.llama3-1-8b-instruct-v1:0": {
					ID:          "meta.llama3-1-8b-instruct-v1:0",
					Name:        "Llama 3.1 8B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.22,
						Output:     0.22,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta.llama3-2-11b-instruct-v1:0": {
					ID:          "meta.llama3-2-11b-instruct-v1:0",
					Name:        "Llama 3.2 11B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.16,
						Output:     0.16,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta.llama3-2-1b-instruct-v1:0": {
					ID:          "meta.llama3-2-1b-instruct-v1:0",
					Name:        "Llama 3.2 1B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131000,
						Output:  4096,
					},
				},
				"meta.llama3-2-3b-instruct-v1:0": {
					ID:          "meta.llama3-2-3b-instruct-v1:0",
					Name:        "Llama 3.2 3B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131000,
						Output:  4096,
					},
				},
				"meta.llama3-2-90b-instruct-v1:0": {
					ID:          "meta.llama3-2-90b-instruct-v1:0",
					Name:        "Llama 3.2 90B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.72,
						Output:     0.72,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta.llama3-3-70b-instruct-v1:0": {
					ID:          "meta.llama3-3-70b-instruct-v1:0",
					Name:        "Llama 3.3 70B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.72,
						Output:     0.72,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta.llama3-70b-instruct-v1:0": {
					ID:          "meta.llama3-70b-instruct-v1:0",
					Name:        "Llama 3 70B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2.65,
						Output:     3.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  2048,
					},
				},
				"meta.llama3-8b-instruct-v1:0": {
					ID:          "meta.llama3-8b-instruct-v1:0",
					Name:        "Llama 3 8B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  2048,
					},
				},
				"meta.llama4-maverick-17b-instruct-v1:0": {
					ID:          "meta.llama4-maverick-17b-instruct-v1:0",
					Name:        "Llama 4 Maverick 17B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.24,
						Output:     0.97,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  16384,
					},
				},
				"meta.llama4-scout-17b-instruct-v1:0": {
					ID:          "meta.llama4-scout-17b-instruct-v1:0",
					Name:        "Llama 4 Scout 17B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.17,
						Output:     0.66,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 3500000,
						Output:  16384,
					},
				},
			},
		},
		"anthropic": {
			ID:   "anthropic",
			Env:  []string{"ANTHROPIC_API_KEY"},
			NPM:  "@ai-sdk/anthropic",
			Name: "Anthropic",
			Models: map[string]ModelInfo{
				"claude-3-5-haiku-20241022": {
					ID:          "claude-3-5-haiku-20241022",
					Name:        "Claude Haiku 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     4,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3-5-sonnet-20240620": {
					ID:          "claude-3-5-sonnet-20240620",
					Name:        "Claude Sonnet 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3-5-sonnet-20241022": {
					ID:          "claude-3-5-sonnet-20241022",
					Name:        "Claude Sonnet 3.5 v2",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3-7-sonnet-20250219": {
					ID:          "claude-3-7-sonnet-20250219",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"claude-3-haiku-20240307": {
					ID:          "claude-3-haiku-20240307",
					Name:        "Claude Haiku 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     1.25,
						CacheRead:  &[]float64{0.03}[0],
						CacheWrite: &[]float64{0.3}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"claude-3-opus-20240229": {
					ID:          "claude-3-opus-20240229",
					Name:        "Claude Opus 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"claude-3-sonnet-20240229": {
					ID:          "claude-3-sonnet-20240229",
					Name:        "Claude Sonnet 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{0.3}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"claude-opus-4-1-20250805": {
					ID:          "claude-opus-4-1-20250805",
					Name:        "Claude Opus 4.1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"claude-opus-4-20250514": {
					ID:          "claude-opus-4-20250514",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"claude-sonnet-4-20250514": {
					ID:          "claude-sonnet-4-20250514",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
			},
		},
		"azure": {
			ID:   "azure",
			Env:  []string{"AZURE_RESOURCE_NAME", "AZURE_API_KEY"},
			NPM:  "@ai-sdk/azure",
			Name: "Azure",
			Models: map[string]ModelInfo{
				"codex-mini": {
					ID:          "codex-mini",
					Name:        "Codex Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.5,
						Output:     6,
						CacheRead:  &[]float64{0.375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"gpt-3.5-turbo-0125": {
					ID:          "gpt-3.5-turbo-0125",
					Name:        "GPT-3.5 Turbo 0125",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     1.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16384,
						Output:  16384,
					},
				},
				"gpt-3.5-turbo-0301": {
					ID:          "gpt-3.5-turbo-0301",
					Name:        "GPT-3.5 Turbo 0301",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4096,
						Output:  4096,
					},
				},
				"gpt-3.5-turbo-0613": {
					ID:          "gpt-3.5-turbo-0613",
					Name:        "GPT-3.5 Turbo 0613",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16384,
						Output:  16384,
					},
				},
				"gpt-3.5-turbo-1106": {
					ID:          "gpt-3.5-turbo-1106",
					Name:        "GPT-3.5 Turbo 1106",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16384,
						Output:  16384,
					},
				},
				"gpt-3.5-turbo-instruct": {
					ID:          "gpt-3.5-turbo-instruct",
					Name:        "GPT-3.5 Turbo Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4096,
						Output:  4096,
					},
				},
				"gpt-4": {
					ID:          "gpt-4",
					Name:        "GPT-4",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      60,
						Output:     120,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"gpt-4-32k": {
					ID:          "gpt-4-32k",
					Name:        "GPT-4 32K",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      60,
						Output:     120,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"gpt-4-turbo": {
					ID:          "gpt-4-turbo",
					Name:        "GPT-4 Turbo",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      10,
						Output:     30,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"gpt-4-turbo-vision": {
					ID:          "gpt-4-turbo-vision",
					Name:        "GPT-4 Turbo Vision",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      10,
						Output:     30,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"gpt-4.1": {
					ID:          "gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"gpt-4.1-mini": {
					ID:          "gpt-4.1-mini",
					Name:        "GPT-4.1 mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     1.6,
						CacheRead:  &[]float64{0.1}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"gpt-4.1-nano": {
					ID:          "gpt-4.1-nano",
					Name:        "GPT-4.1 nano",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.03}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"gpt-4o": {
					ID:          "gpt-4o",
					Name:        "GPT-4o",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2.5,
						Output:     10,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"gpt-4o-mini": {
					ID:          "gpt-4o-mini",
					Name:        "GPT-4o mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"o1": {
					ID:          "o1",
					Name:        "o1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      15,
						Output:     60,
						CacheRead:  &[]float64{7.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o1-mini": {
					ID:          "o1-mini",
					Name:        "o1-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.55}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  65536,
					},
				},
				"o1-preview": {
					ID:          "o1-preview",
					Name:        "o1-preview",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      16.5,
						Output:     66,
						CacheRead:  &[]float64{8.25}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"o3": {
					ID:          "o3",
					Name:        "o3",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o3-mini": {
					ID:          "o3-mini",
					Name:        "o3-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.55}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o4-mini": {
					ID:          "o4-mini",
					Name:        "o4-mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.28}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
			},
		},
		"cerebras": {
			ID:   "cerebras",
			Env:  []string{"CEREBRAS_API_KEY"},
			NPM:  "@ai-sdk/cerebras",
			Name: "Cerebras",
			Models: map[string]ModelInfo{
				"gpt-oss-120b": {
					ID:          "gpt-oss-120b",
					Name:        "GPT OSS 120B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     0.69,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"qwen-3-coder-480b": {
					ID:          "qwen-3-coder-480b",
					Name:        "Qwen 3 Coder 480B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131000,
						Output:  32000,
					},
				},
			},
		},
		"chutes": {
			ID:   "chutes",
			Env:  []string{"CHUTES_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Chutes",
			Models: map[string]ModelInfo{
				"Qwen/Qwen3-235B-A22B-Instruct-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Instruct-2507",
					Name:        "Qwen3 235B A22B Instruct 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.078,
						Output:     0.312,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"Qwen/Qwen3-235B-A22B-Thinking-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Thinking-2507",
					Name:        "Qwen3-235B-A22B-Thinking-2507",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.078,
						Output:     0.312,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  262144,
					},
				},
				"Qwen/Qwen3-30B-A3B": {
					ID:          "Qwen/Qwen3-30B-A3B",
					Name:        "Qwen3 30B A3B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.02,
						Output:     0.08,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 40960,
						Output:  40960,
					},
				},
				"Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8",
					Name:        "Qwen3 Coder 480B A35B Instruct (FP8)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  262144,
					},
				},
				"chutesai/Devstral-Small-2505": {
					ID:          "chutesai/Devstral-Small-2505",
					Name:        "Devstral Small (2505)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.02,
						Output:     0.08,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"chutesai/Mistral-Small-3.2-24B-Instruct-2506": {
					ID:          "chutesai/Mistral-Small-3.2-24B-Instruct-2506",
					Name:        "Mistral Small 3.2 24B Instruct (2506)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.02,
						Output:     0.08,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"deepseek-ai/DeepSeek-R1-0528": {
					ID:          "deepseek-ai/DeepSeek-R1-0528",
					Name:        "DeepSeek R1 (0528)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.18,
						Output:     0.72,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 75000,
						Output:  163840,
					},
				},
				"deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {
					ID:          "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
					Name:        "DeepSeek R1 0528 Qwen3 8B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.02,
						Output:     0.07,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {
					ID:          "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
					Name:        "DeepSeek R1 Distill Llama 70B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.03,
						Output:     0.14,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"deepseek-ai/DeepSeek-V3-0324": {
					ID:          "deepseek-ai/DeepSeek-V3-0324",
					Name:        "DeepSeek V3 (0324)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.18,
						Output:     0.72,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 75000,
						Output:  163840,
					},
				},
				"moonshotai/Kimi-K2-Instruct-75k": {
					ID:          "moonshotai/Kimi-K2-Instruct-75k",
					Name:        "Kimi K2 Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.59,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 75000,
						Output:  75000,
					},
				},
				"tngtech/DeepSeek-R1T-Chimera": {
					ID:          "tngtech/DeepSeek-R1T-Chimera",
					Name:        "DeepSeek R1T Chimera",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.18,
						Output:     0.72,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"tngtech/DeepSeek-TNG-R1T2-Chimera": {
					ID:          "tngtech/DeepSeek-TNG-R1T2-Chimera",
					Name:        "DeepSeek TNG R1T2 Chimera",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"zai-org/GLM-4.5-Air": {
					ID:          "zai-org/GLM-4.5-Air",
					Name:        "GLM 4.5 Air",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
				"zai-org/GLM-4.5-FP8": {
					ID:          "zai-org/GLM-4.5-FP8",
					Name:        "GLM 4.5 FP8",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
			},
		},
		"deepinfra": {
			ID:   "deepinfra",
			Env:  []string{"DEEPINFRA_API_KEY"},
			NPM:  "@ai-sdk/deepinfra",
			Name: "Deep Infra",
			Models: map[string]ModelInfo{
				"Qwen/Qwen3-Coder-480B-A35B-Instruct": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct",
					Name:        "Qwen3 Coder 480B A35B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     1.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"Qwen/Qwen3-Coder-480B-A35B-Instruct-Turbo": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct-Turbo",
					Name:        "Qwen3 Coder 480B A35B Instruct Turbo",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     1.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"moonshotai/Kimi-K2-Instruct": {
					ID:          "moonshotai/Kimi-K2-Instruct",
					Name:        "Kimi K2",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"zai-org/GLM-4.5": {
					ID:          "zai-org/GLM-4.5",
					Name:        "GLM-4.5",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     2.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
			},
		},
		"deepseek": {
			ID:   "deepseek",
			Env:  []string{"DEEPSEEK_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "DeepSeek",
			Models: map[string]ModelInfo{
				"deepseek-chat": {
					ID:          "deepseek-chat",
					Name:        "DeepSeek Chat",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.27,
						Output:     1.1,
						CacheRead:  &[]float64{0.07}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
				"deepseek-reasoner": {
					ID:          "deepseek-reasoner",
					Name:        "DeepSeek Reasoner",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.55,
						Output:     2.19,
						CacheRead:  &[]float64{0.14}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
			},
		},
		"fireworks-ai": {
			ID:   "fireworks-ai",
			Env:  []string{"FIREWORKS_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Fireworks AI",
			Models: map[string]ModelInfo{
				"accounts/fireworks/gpt-oss-120b": {
					ID:          "accounts/fireworks/gpt-oss-120b",
					Name:        "GPT OSS 120B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"accounts/fireworks/gpt-oss-20b": {
					ID:          "accounts/fireworks/gpt-oss-20b",
					Name:        "GPT OSS 20B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.05,
						Output:     0.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"accounts/fireworks/models/deepseek-r1-0528": {
					ID:          "accounts/fireworks/models/deepseek-r1-0528",
					Name:        "Deepseek R1 05/28",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 160000,
						Output:  16384,
					},
				},
				"accounts/fireworks/models/deepseek-v3-0324": {
					ID:          "accounts/fireworks/models/deepseek-v3-0324",
					Name:        "Deepseek V3 03-24",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.9,
						Output:     0.9,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 160000,
						Output:  16384,
					},
				},
				"accounts/fireworks/models/kimi-k2-instruct": {
					ID:          "accounts/fireworks/models/kimi-k2-instruct",
					Name:        "Kimi K2 Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"accounts/fireworks/models/qwen3-235b-a22b": {
					ID:          "accounts/fireworks/models/qwen3-235b-a22b",
					Name:        "Qwen3 235B-A22B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.22,
						Output:     0.88,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
			},
		},
		"github-copilot": {
			ID:   "github-copilot",
			Env:  []string{"GITHUB_TOKEN"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "GitHub Copilot",
			Models: map[string]ModelInfo{
				"claude-3.5-sonnet": {
					ID:          "claude-3.5-sonnet",
					Name:        "Claude Sonnet 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3.7-sonnet": {
					ID:          "claude-3.7-sonnet",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3.7-sonnet-thought": {
					ID:          "claude-3.7-sonnet-thought",
					Name:        "Claude Sonnet 3.7 Thinking",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-opus-4": {
					ID:          "claude-opus-4",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 80000,
						Output:  16000,
					},
				},
				"claude-opus-4.1": {
					ID:          "claude-opus-4.1",
					Name:        "Claude Opus 4.1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"claude-sonnet-4": {
					ID:          "claude-sonnet-4",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"gemini-2.0-flash-001": {
					ID:          "gemini-2.0-flash-001",
					Name:        "Gemini 2.0 Flash",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  8192,
					},
				},
				"gemini-2.5-pro": {
					ID:          "gemini-2.5-pro",
					Name:        "Gemini 2.5 Pro (Preview)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gpt-4.1": {
					ID:          "gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"gpt-4o": {
					ID:          "gpt-4o",
					Name:        "GPT-4o",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"gpt-5": {
					ID:          "gpt-5",
					Name:        "GPT-5",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"o3": {
					ID:          "o3",
					Name:        "o3 (Preview)",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"o3-mini": {
					ID:          "o3-mini",
					Name:        "o3-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  65536,
					},
				},
				"o4-mini": {
					ID:          "o4-mini",
					Name:        "o4-mini (Preview)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  65536,
					},
				},
			},
		},
		"github-models": {
			ID:   "github-models",
			Env:  []string{"GITHUB_TOKEN"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "GitHub Models",
			Models: map[string]ModelInfo{
				"ai21-labs/ai21-jamba-1.5-large": {
					ID:          "ai21-labs/ai21-jamba-1.5-large",
					Name:        "AI21 Jamba 1.5 Large",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  4096,
					},
				},
				"ai21-labs/ai21-jamba-1.5-mini": {
					ID:          "ai21-labs/ai21-jamba-1.5-mini",
					Name:        "AI21 Jamba 1.5 Mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  4096,
					},
				},
				"cohere/cohere-command-a": {
					ID:          "cohere/cohere-command-a",
					Name:        "Cohere Command A",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cohere/cohere-command-r": {
					ID:          "cohere/cohere-command-r",
					Name:        "Cohere Command R",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cohere/cohere-command-r-08-2024": {
					ID:          "cohere/cohere-command-r-08-2024",
					Name:        "Cohere Command R 08-2024",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cohere/cohere-command-r-plus": {
					ID:          "cohere/cohere-command-r-plus",
					Name:        "Cohere Command R+",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cohere/cohere-command-r-plus-08-2024": {
					ID:          "cohere/cohere-command-r-plus-08-2024",
					Name:        "Cohere Command R+ 08-2024",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"core42/jais-30b-chat": {
					ID:          "core42/jais-30b-chat",
					Name:        "JAIS 30b Chat",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  2048,
					},
				},
				"deepseek/deepseek-r1": {
					ID:          "deepseek/deepseek-r1",
					Name:        "DeepSeek-R1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
				"deepseek/deepseek-r1-0528": {
					ID:          "deepseek/deepseek-r1-0528",
					Name:        "DeepSeek-R1-0528",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
				"deepseek/deepseek-v3-0324": {
					ID:          "deepseek/deepseek-v3-0324",
					Name:        "DeepSeek-V3-0324",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"meta/llama-3.2-11b-vision-instruct": {
					ID:          "meta/llama-3.2-11b-vision-instruct",
					Name:        "Llama-3.2-11B-Vision-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"meta/llama-3.2-90b-vision-instruct": {
					ID:          "meta/llama-3.2-90b-vision-instruct",
					Name:        "Llama-3.2-90B-Vision-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"meta/llama-3.3-70b-instruct": {
					ID:          "meta/llama-3.3-70b-instruct",
					Name:        "Llama-3.3-70B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"meta/llama-4-maverick-17b-128e-instruct-fp8": {
					ID:          "meta/llama-4-maverick-17b-128e-instruct-fp8",
					Name:        "Llama 4 Maverick 17B 128E Instruct FP8",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"meta/llama-4-scout-17b-16e-instruct": {
					ID:          "meta/llama-4-scout-17b-16e-instruct",
					Name:        "Llama 4 Scout 17B 16E Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"meta/meta-llama-3-70b-instruct": {
					ID:          "meta/meta-llama-3-70b-instruct",
					Name:        "Meta-Llama-3-70B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  2048,
					},
				},
				"meta/meta-llama-3-8b-instruct": {
					ID:          "meta/meta-llama-3-8b-instruct",
					Name:        "Meta-Llama-3-8B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  2048,
					},
				},
				"meta/meta-llama-3.1-405b-instruct": {
					ID:          "meta/meta-llama-3.1-405b-instruct",
					Name:        "Meta-Llama-3.1-405B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"meta/meta-llama-3.1-70b-instruct": {
					ID:          "meta/meta-llama-3.1-70b-instruct",
					Name:        "Meta-Llama-3.1-70B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"meta/meta-llama-3.1-8b-instruct": {
					ID:          "meta/meta-llama-3.1-8b-instruct",
					Name:        "Meta-Llama-3.1-8B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"microsoft/mai-ds-r1": {
					ID:          "microsoft/mai-ds-r1",
					Name:        "MAI-DS-R1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
				"microsoft/phi-3-medium-128k-instruct": {
					ID:          "microsoft/phi-3-medium-128k-instruct",
					Name:        "Phi-3-medium instruct (128k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-3-medium-4k-instruct": {
					ID:          "microsoft/phi-3-medium-4k-instruct",
					Name:        "Phi-3-medium instruct (4k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4096,
						Output:  1024,
					},
				},
				"microsoft/phi-3-mini-128k-instruct": {
					ID:          "microsoft/phi-3-mini-128k-instruct",
					Name:        "Phi-3-mini instruct (128k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-3-mini-4k-instruct": {
					ID:          "microsoft/phi-3-mini-4k-instruct",
					Name:        "Phi-3-mini instruct (4k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4096,
						Output:  1024,
					},
				},
				"microsoft/phi-3-small-128k-instruct": {
					ID:          "microsoft/phi-3-small-128k-instruct",
					Name:        "Phi-3-small instruct (128k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-3-small-8k-instruct": {
					ID:          "microsoft/phi-3-small-8k-instruct",
					Name:        "Phi-3-small instruct (8k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  2048,
					},
				},
				"microsoft/phi-3.5-mini-instruct": {
					ID:          "microsoft/phi-3.5-mini-instruct",
					Name:        "Phi-3.5-mini instruct (128k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-3.5-moe-instruct": {
					ID:          "microsoft/phi-3.5-moe-instruct",
					Name:        "Phi-3.5-MoE instruct (128k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-3.5-vision-instruct": {
					ID:          "microsoft/phi-3.5-vision-instruct",
					Name:        "Phi-3.5-vision instruct (128k)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-4": {
					ID:          "microsoft/phi-4",
					Name:        "Phi-4",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  4096,
					},
				},
				"microsoft/phi-4-mini-instruct": {
					ID:          "microsoft/phi-4-mini-instruct",
					Name:        "Phi-4-mini-instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-4-mini-reasoning": {
					ID:          "microsoft/phi-4-mini-reasoning",
					Name:        "Phi-4-mini-reasoning",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-4-multimodal-instruct": {
					ID:          "microsoft/phi-4-multimodal-instruct",
					Name:        "Phi-4-multimodal-instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"microsoft/phi-4-reasoning": {
					ID:          "microsoft/phi-4-reasoning",
					Name:        "Phi-4-Reasoning",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"mistral-ai/codestral-2501": {
					ID:          "mistral-ai/codestral-2501",
					Name:        "Codestral 25.01",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32000,
						Output:  8192,
					},
				},
				"mistral-ai/ministral-3b": {
					ID:          "mistral-ai/ministral-3b",
					Name:        "Ministral 3B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"mistral-ai/mistral-large-2411": {
					ID:          "mistral-ai/mistral-large-2411",
					Name:        "Mistral Large 24.11",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"mistral-ai/mistral-medium-2505": {
					ID:          "mistral-ai/mistral-medium-2505",
					Name:        "Mistral Medium 3 (25.05)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"mistral-ai/mistral-nemo": {
					ID:          "mistral-ai/mistral-nemo",
					Name:        "Mistral Nemo",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"mistral-ai/mistral-small-2503": {
					ID:          "mistral-ai/mistral-small-2503",
					Name:        "Mistral Small 3.1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"openai/gpt-4.1": {
					ID:          "openai/gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-4.1-mini": {
					ID:          "openai/gpt-4.1-mini",
					Name:        "GPT-4.1-mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-4.1-nano": {
					ID:          "openai/gpt-4.1-nano",
					Name:        "GPT-4.1-nano",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-4o": {
					ID:          "openai/gpt-4o",
					Name:        "GPT-4o",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-4o-mini": {
					ID:          "openai/gpt-4o-mini",
					Name:        "GPT-4o mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/o1": {
					ID:          "openai/o1",
					Name:        "OpenAI o1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openai/o1-mini": {
					ID:          "openai/o1-mini",
					Name:        "OpenAI o1-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  65536,
					},
				},
				"openai/o1-preview": {
					ID:          "openai/o1-preview",
					Name:        "OpenAI o1-preview",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"openai/o3": {
					ID:          "openai/o3",
					Name:        "OpenAI o3",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openai/o3-mini": {
					ID:          "openai/o3-mini",
					Name:        "OpenAI o3-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openai/o4-mini": {
					ID:          "openai/o4-mini",
					Name:        "OpenAI o4-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"xai/grok-3": {
					ID:          "xai/grok-3",
					Name:        "Grok 3",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"xai/grok-3-mini": {
					ID:          "xai/grok-3-mini",
					Name:        "Grok 3 Mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
			},
		},
		"google": {
			ID:   "google",
			Env:  []string{"GOOGLE_API_KEY", "GEMINI_API_KEY", "GOOGLE_GENERATIVE_AI_API_KEY"},
			NPM:  "@ai-sdk/google",
			Name: "Google",
			Models: map[string]ModelInfo{
				"gemini-1.5-flash": {
					ID:          "gemini-1.5-flash",
					Name:        "Gemini 1.5 Flash",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.075,
						Output:     0.3,
						CacheRead:  &[]float64{0.01875}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  8192,
					},
				},
				"gemini-1.5-flash-8b": {
					ID:          "gemini-1.5-flash-8b",
					Name:        "Gemini 1.5 Flash-8B",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.0375,
						Output:     0.15,
						CacheRead:  &[]float64{0.01}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  8192,
					},
				},
				"gemini-1.5-pro": {
					ID:          "gemini-1.5-pro",
					Name:        "Gemini 1.5 Pro",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     5,
						CacheRead:  &[]float64{0.3125}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  8192,
					},
				},
				"gemini-2.0-flash": {
					ID:          "gemini-2.0-flash",
					Name:        "Gemini 2.0 Flash",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.025}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"gemini-2.0-flash-lite": {
					ID:          "gemini-2.0-flash-lite",
					Name:        "Gemini 2.0 Flash Lite",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.075,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"gemini-2.5-flash": {
					ID:          "gemini-2.5-flash",
					Name:        "Gemini 2.5 Flash",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     2.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-flash-lite-preview-06-17": {
					ID:          "gemini-2.5-flash-lite-preview-06-17",
					Name:        "Gemini 2.5 Flash Lite Preview 06-17",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.025}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  65536,
					},
				},
				"gemini-2.5-flash-preview-04-17": {
					ID:          "gemini-2.5-flash-preview-04-17",
					Name:        "Gemini 2.5 Flash Preview 04-17",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.0375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-flash-preview-05-20": {
					ID:          "gemini-2.5-flash-preview-05-20",
					Name:        "Gemini 2.5 Flash Preview 05-20",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.0375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-pro": {
					ID:          "gemini-2.5-pro",
					Name:        "Gemini 2.5 Pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-pro-preview-05-06": {
					ID:          "gemini-2.5-pro-preview-05-06",
					Name:        "Gemini 2.5 Pro Preview 05-06",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-pro-preview-06-05": {
					ID:          "gemini-2.5-pro-preview-06-05",
					Name:        "Gemini 2.5 Pro Preview 06-05",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
			},
		},
		"google-vertex": {
			ID:   "google-vertex",
			Env:  []string{"GOOGLE_VERTEX_PROJECT", "GOOGLE_VERTEX_LOCATION", "GOOGLE_APPLICATION_CREDENTIALS"},
			NPM:  "@ai-sdk/google-vertex",
			Name: "Vertex",
			Models: map[string]ModelInfo{
				"gemini-2.0-flash": {
					ID:          "gemini-2.0-flash",
					Name:        "Gemini 2.0 Flash",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.025}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"gemini-2.0-flash-lite": {
					ID:          "gemini-2.0-flash-lite",
					Name:        "Gemini 2.0 Flash Lite",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.075,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"gemini-2.5-flash-lite-preview-06-17": {
					ID:          "gemini-2.5-flash-lite-preview-06-17",
					Name:        "Gemini 2.5 Flash Lite Preview 06-17",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.025}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  65536,
					},
				},
				"gemini-2.5-flash-preview-04-17": {
					ID:          "gemini-2.5-flash-preview-04-17",
					Name:        "Gemini 2.5 Flash Preview 04-17",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.0375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-flash-preview-05-20": {
					ID:          "gemini-2.5-flash-preview-05-20",
					Name:        "Gemini 2.5 Flash Preview 05-20",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.0375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-pro": {
					ID:          "gemini-2.5-pro",
					Name:        "Gemini 2.5 Pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-pro-preview-05-06": {
					ID:          "gemini-2.5-pro-preview-05-06",
					Name:        "Gemini 2.5 Pro Preview 05-06",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"gemini-2.5-pro-preview-06-05": {
					ID:          "gemini-2.5-pro-preview-06-05",
					Name:        "Gemini 2.5 Pro Preview 06-05",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
			},
		},
		"google-vertex-anthropic": {
			ID:   "google-vertex-anthropic",
			Env:  []string{"GOOGLE_VERTEX_PROJECT", "GOOGLE_VERTEX_LOCATION", "GOOGLE_APPLICATION_CREDENTIALS"},
			NPM:  "@ai-sdk/google-vertex/anthropic",
			Name: "Vertex",
			Models: map[string]ModelInfo{
				"claude-3-5-haiku@20241022": {
					ID:          "claude-3-5-haiku@20241022",
					Name:        "Claude Haiku 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     4,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3-5-sonnet@20241022": {
					ID:          "claude-3-5-sonnet@20241022",
					Name:        "Claude Sonnet 3.5 v2",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"claude-3-7-sonnet@20250219": {
					ID:          "claude-3-7-sonnet@20250219",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"claude-opus-4-1@20250805": {
					ID:          "claude-opus-4-1@20250805",
					Name:        "Claude Opus 4.1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"claude-opus-4@20250514": {
					ID:          "claude-opus-4@20250514",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"claude-sonnet-4@20250514": {
					ID:          "claude-sonnet-4@20250514",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
			},
		},
		"groq": {
			ID:   "groq",
			Env:  []string{"GROQ_API_KEY"},
			NPM:  "@ai-sdk/groq",
			Name: "Groq",
			Models: map[string]ModelInfo{
				"deepseek-r1-distill-llama-70b": {
					ID:          "deepseek-r1-distill-llama-70b",
					Name:        "DeepSeek R1 Distill Llama 70B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.75,
						Output:     0.99,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"gemma2-9b-it": {
					ID:          "gemma2-9b-it",
					Name:        "Gemma 2 9B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"llama-3.1-8b-instant": {
					ID:          "llama-3.1-8b-instant",
					Name:        "Llama 3.1 8B Instant",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.05,
						Output:     0.08,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"llama-3.3-70b-versatile": {
					ID:          "llama-3.3-70b-versatile",
					Name:        "Llama 3.3 70B Versatile",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.59,
						Output:     0.79,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"llama-guard-3-8b": {
					ID:          "llama-guard-3-8b",
					Name:        "Llama Guard 3 8B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"llama3-70b-8192": {
					ID:          "llama3-70b-8192",
					Name:        "Llama 3 70B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.59,
						Output:     0.79,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"llama3-8b-8192": {
					ID:          "llama3-8b-8192",
					Name:        "Llama 3 8B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.05,
						Output:     0.08,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"meta-llama/llama-4-maverick-17b-128e-instruct": {
					ID:          "meta-llama/llama-4-maverick-17b-128e-instruct",
					Name:        "Llama 4 Maverick 17B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"meta-llama/llama-4-scout-17b-16e-instruct": {
					ID:          "meta-llama/llama-4-scout-17b-16e-instruct",
					Name:        "Llama 4 Scout 17B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.11,
						Output:     0.34,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"meta-llama/llama-guard-4-12b": {
					ID:          "meta-llama/llama-guard-4-12b",
					Name:        "Llama Guard 4 12B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  128,
					},
				},
				"mistral-saba-24b": {
					ID:          "mistral-saba-24b",
					Name:        "Mistral Saba 24B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.79,
						Output:     0.79,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"moonshotai/kimi-k2-instruct": {
					ID:          "moonshotai/kimi-k2-instruct",
					Name:        "Kimi K2 Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
				"openai/gpt-oss-120b": {
					ID:          "openai/gpt-oss-120b",
					Name:        "GPT OSS 120B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.75,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"openai/gpt-oss-20b": {
					ID:          "openai/gpt-oss-20b",
					Name:        "GPT OSS 20B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"qwen-qwq-32b": {
					ID:          "qwen-qwq-32b",
					Name:        "Qwen QwQ 32B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.29,
						Output:     0.39,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
				"qwen/qwen3-32b": {
					ID:          "qwen/qwen3-32b",
					Name:        "Qwen3 32B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.29,
						Output:     0.59,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
			},
		},
		"huggingface": {
			ID:   "huggingface",
			Env:  []string{"HF_TOKEN"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Hugging Face",
			Models: map[string]ModelInfo{
				"Qwen/Qwen3-235B-A22B-Thinking-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Thinking-2507",
					Name:        "Qwen3-235B-A22B-Thinking-2507",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"Qwen/Qwen3-Coder-480B-A35B-Instruct": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct",
					Name:        "Qwen3-Coder-480B-A35B-Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"deepseek-ai/DeepSeek-R1-0528": {
					ID:          "deepseek-ai/DeepSeek-R1-0528",
					Name:        "DeepSeek-R1-0528",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"deepseek-ai/Deepseek-V3-0324": {
					ID:          "deepseek-ai/Deepseek-V3-0324",
					Name:        "DeepSeek-V3-0324",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     1.25,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16384,
						Output:  8192,
					},
				},
				"moonshotai/Kimi-K2-Instruct": {
					ID:          "moonshotai/Kimi-K2-Instruct",
					Name:        "Kimi-K2-Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
				"zai-org/GLM-4.5": {
					ID:          "zai-org/GLM-4.5",
					Name:        "GLM-4.5",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     2.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
				"zai-org/GLM-4.5-Air": {
					ID:          "zai-org/GLM-4.5-Air",
					Name:        "GLM-4.5-Air",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     1.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  96000,
					},
				},
			},
		},
		"inception": {
			ID:   "inception",
			Env:  []string{"INCEPTION_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Inception",
			Models: map[string]ModelInfo{
				"mercury": {
					ID:          "mercury",
					Name:        "Mercury",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     1,
						CacheRead:  &[]float64{0.25}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"mercury-coder": {
					ID:          "mercury-coder",
					Name:        "Mercury Coder",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     1,
						CacheRead:  &[]float64{0.25}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
			},
		},
		"inference": {
			ID:   "inference",
			Env:  []string{"INFERENCE_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Inference",
			Models: map[string]ModelInfo{
				"google/gemma-3": {
					ID:          "google/gemma-3",
					Name:        "Google Gemma 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 125000,
						Output:  4096,
					},
				},
				"meta/llama-3.1-8b-instruct": {
					ID:          "meta/llama-3.1-8b-instruct",
					Name:        "Llama 3.1 8B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.025,
						Output:     0.025,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  4096,
					},
				},
				"meta/llama-3.2-11b-vision-instruct": {
					ID:          "meta/llama-3.2-11b-vision-instruct",
					Name:        "Llama 3.2 11B Vision Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.055,
						Output:     0.055,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  4096,
					},
				},
				"meta/llama-3.2-1b-instruct": {
					ID:          "meta/llama-3.2-1b-instruct",
					Name:        "Llama 3.2 1B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.01,
						Output:     0.01,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  4096,
					},
				},
				"meta/llama-3.2-3b-instruct": {
					ID:          "meta/llama-3.2-3b-instruct",
					Name:        "Llama 3.2 3B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.02,
						Output:     0.02,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  4096,
					},
				},
				"mistral/mistral-nemo-12b-instruct": {
					ID:          "mistral/mistral-nemo-12b-instruct",
					Name:        "Mistral Nemo 12B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.038,
						Output:     0.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  4096,
					},
				},
				"osmosis/osmosis-structure-0.6b": {
					ID:          "osmosis/osmosis-structure-0.6b",
					Name:        "Osmosis Structure 0.6B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 4000,
						Output:  2048,
					},
				},
				"qwen/qwen-2.5-7b-vision-instruct": {
					ID:          "qwen/qwen-2.5-7b-vision-instruct",
					Name:        "Qwen 2.5 7B Vision Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 125000,
						Output:  4096,
					},
				},
				"qwen/qwen3-embedding-4b": {
					ID:          "qwen/qwen3-embedding-4b",
					Name:        "Qwen 3 Embedding 4B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0.01,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32000,
						Output:  2048,
					},
				},
			},
		},
		"llama": {
			ID:   "llama",
			Env:  []string{"LLAMA_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Llama",
			Models: map[string]ModelInfo{
				"cerebras-llama-4-maverick-17b-128e-instruct": {
					ID:          "cerebras-llama-4-maverick-17b-128e-instruct",
					Name:        "Cerebras-Llama-4-Maverick-17B-128E-Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"cerebras-llama-4-scout-17b-16e-instruct": {
					ID:          "cerebras-llama-4-scout-17b-16e-instruct",
					Name:        "Cerebras-Llama-4-Scout-17B-16E-Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"groq-llama-4-maverick-17b-128e-instruct": {
					ID:          "groq-llama-4-maverick-17b-128e-instruct",
					Name:        "Groq-Llama-4-Maverick-17B-128E-Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"llama-3.3-70b-instruct": {
					ID:          "llama-3.3-70b-instruct",
					Name:        "Llama-3.3-70B-Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"llama-3.3-8b-instruct": {
					ID:          "llama-3.3-8b-instruct",
					Name:        "Llama-3.3-8B-Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"llama-4-maverick-17b-128e-instruct-fp8": {
					ID:          "llama-4-maverick-17b-128e-instruct-fp8",
					Name:        "Llama-4-Maverick-17B-128E-Instruct-FP8",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"llama-4-scout-17b-16e-instruct-fp8": {
					ID:          "llama-4-scout-17b-16e-instruct-fp8",
					Name:        "Llama-4-Scout-17B-16E-Instruct-FP8",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
			},
		},
		"lmstudio": {
			ID:   "lmstudio",
			Env:  []string{"LMSTUDIO_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "LMStudio",
			Models: map[string]ModelInfo{
				"qwen/qwen3-30b-a3b-2507": {
					ID:          "qwen/qwen3-30b-a3b-2507",
					Name:        "Qwen3 30B A3B 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  16384,
					},
				},
				"qwen/qwen3-coder-30b": {
					ID:          "qwen/qwen3-coder-30b",
					Name:        "Qwen3 Coder 30B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  65536,
					},
				},
			},
		},
		"mistral": {
			ID:   "mistral",
			Env:  []string{"MISTRAL_API_KEY"},
			NPM:  "@ai-sdk/mistral",
			Name: "Mistral",
			Models: map[string]ModelInfo{
				"codestral-latest": {
					ID:          "codestral-latest",
					Name:        "Codestral",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.9,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  4096,
					},
				},
				"devstral-medium-2507": {
					ID:          "devstral-medium-2507",
					Name:        "Devstral Medium",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"devstral-small-2505": {
					ID:          "devstral-small-2505",
					Name:        "Devstral Small 2505",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"devstral-small-2507": {
					ID:          "devstral-small-2507",
					Name:        "Devstral Small",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"magistral-medium-latest": {
					ID:          "magistral-medium-latest",
					Name:        "Magistral Medium",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"magistral-small": {
					ID:          "magistral-small",
					Name:        "Magistral Small",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     1.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"ministral-3b-latest": {
					ID:          "ministral-3b-latest",
					Name:        "Ministral 3B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.04,
						Output:     0.04,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"ministral-8b-latest": {
					ID:          "ministral-8b-latest",
					Name:        "Ministral 8B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistral-large-latest": {
					ID:          "mistral-large-latest",
					Name:        "Mistral Large",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
				"mistral-medium-latest": {
					ID:          "mistral-medium-latest",
					Name:        "Mistral Medium",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"mistral-nemo": {
					ID:          "mistral-nemo",
					Name:        "Mistral Nemo",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistral-small-latest": {
					ID:          "mistral-small-latest",
					Name:        "Mistral Small",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"open-mistral-7b": {
					ID:          "open-mistral-7b",
					Name:        "Mistral 7B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     0.25,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8000,
						Output:  8000,
					},
				},
				"open-mixtral-8x22b": {
					ID:          "open-mixtral-8x22b",
					Name:        "Mixtral 8x22B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 64000,
						Output:  64000,
					},
				},
				"open-mixtral-8x7b": {
					ID:          "open-mixtral-8x7b",
					Name:        "Mixtral 8x7B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.7,
						Output:     0.7,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32000,
						Output:  32000,
					},
				},
				"pixtral-12b": {
					ID:          "pixtral-12b",
					Name:        "Pixtral 12B",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"pixtral-large-latest": {
					ID:          "pixtral-large-latest",
					Name:        "Pixtral Large",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
			},
		},
		"modelscope": {
			ID:   "modelscope",
			Env:  []string{"MODELSCOPE_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "ModelScope",
			Models: map[string]ModelInfo{
				"Qwen/Qwen3-235B-A22B-Instruct-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Instruct-2507",
					Name:        "Qwen3 235B A22B Instruct 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"Qwen/Qwen3-235B-A22B-Thinking-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Thinking-2507",
					Name:        "Qwen3-235B-A22B-Thinking-2507",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"Qwen/Qwen3-30B-A3B-Instruct-2507": {
					ID:          "Qwen/Qwen3-30B-A3B-Instruct-2507",
					Name:        "Qwen3 30B A3B Instruct 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  16384,
					},
				},
				"Qwen/Qwen3-30B-A3B-Thinking-2507": {
					ID:          "Qwen/Qwen3-30B-A3B-Thinking-2507",
					Name:        "Qwen3 30B A3B Thinking 2507",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  32768,
					},
				},
				"Qwen/Qwen3-Coder-30B-A3B-Instruct": {
					ID:          "Qwen/Qwen3-Coder-30B-A3B-Instruct",
					Name:        "Qwen3 Coder 30B A3B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  65536,
					},
				},
				"Qwen/Qwen3-Coder-480B-A35B-Instruct": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct",
					Name:        "Qwen3-Coder-480B-A35B-Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"ZhipuAI/GLM-4.5": {
					ID:          "ZhipuAI/GLM-4.5",
					Name:        "GLM-4.5",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
				"moonshotai/Kimi-K2-Instruct": {
					ID:          "moonshotai/Kimi-K2-Instruct",
					Name:        "Kimi-K2-Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
			},
		},
		"morph": {
			ID:   "morph",
			Env:  []string{"MORPH_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Morph",
			Models: map[string]ModelInfo{
				"auto": {
					ID:          "auto",
					Name:        "Auto",
					Attachment:  false,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0.85,
						Output:     1.55,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32000,
						Output:  32000,
					},
				},
				"morph-v3-fast": {
					ID:          "morph-v3-fast",
					Name:        "Morph v3 Fast",
					Attachment:  false,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0.8,
						Output:     1.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  16000,
					},
				},
				"morph-v3-large": {
					ID:          "morph-v3-large",
					Name:        "Morph v3 Large",
					Attachment:  false,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0.9,
						Output:     1.9,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32000,
						Output:  32000,
					},
				},
			},
		},
		"openai": {
			ID:   "openai",
			Env:  []string{"OPENAI_API_KEY"},
			NPM:  "@ai-sdk/openai",
			Name: "OpenAI",
			Models: map[string]ModelInfo{
				"codex-mini-latest": {
					ID:          "codex-mini-latest",
					Name:        "Codex Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.5,
						Output:     6,
						CacheRead:  &[]float64{0.375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"gpt-3.5-turbo": {
					ID:          "gpt-3.5-turbo",
					Name:        "GPT-3.5-turbo",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     1.5,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16385,
						Output:  4096,
					},
				},
				"gpt-4": {
					ID:          "gpt-4",
					Name:        "GPT-4",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      10,
						Output:     30,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"gpt-4-turbo": {
					ID:          "gpt-4-turbo",
					Name:        "GPT-4 Turbo",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      10,
						Output:     30,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"gpt-4.1": {
					ID:          "gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"gpt-4.1-mini": {
					ID:          "gpt-4.1-mini",
					Name:        "GPT-4.1 mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     1.6,
						CacheRead:  &[]float64{0.1}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"gpt-4.1-nano": {
					ID:          "gpt-4.1-nano",
					Name:        "GPT-4.1 nano",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.03}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"gpt-4o": {
					ID:          "gpt-4o",
					Name:        "GPT-4o",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2.5,
						Output:     10,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"gpt-4o-mini": {
					ID:          "gpt-4o-mini",
					Name:        "GPT-4o mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"gpt-5": {
					ID:          "gpt-5",
					Name:        "GPT-5",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"gpt-5-mini": {
					ID:          "gpt-5-mini",
					Name:        "GPT-5 Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0.25,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"gpt-5-nano": {
					ID:          "gpt-5-nano",
					Name:        "GPT-5 Nano",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0.05,
						Output:     0.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"o1": {
					ID:          "o1",
					Name:        "o1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      15,
						Output:     60,
						CacheRead:  &[]float64{7.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o1-mini": {
					ID:          "o1-mini",
					Name:        "o1-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.55}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  65536,
					},
				},
				"o1-preview": {
					ID:          "o1-preview",
					Name:        "o1-preview",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     60,
						CacheRead:  &[]float64{7.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"o1-pro": {
					ID:          "o1-pro",
					Name:        "o1-pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      150,
						Output:     600,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o3": {
					ID:          "o3",
					Name:        "o3",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o3-deep-research": {
					ID:          "o3-deep-research",
					Name:        "o3-deep-research",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      10,
						Output:     40,
						CacheRead:  &[]float64{2.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o3-mini": {
					ID:          "o3-mini",
					Name:        "o3-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.55}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o3-pro": {
					ID:          "o3-pro",
					Name:        "o3-pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      20,
						Output:     80,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o4-mini": {
					ID:          "o4-mini",
					Name:        "o4-mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.28}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"o4-mini-deep-research": {
					ID:          "o4-mini-deep-research",
					Name:        "o4-mini-deep-research",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
			},
		},
		"opencode": {
			ID:   "opencode",
			Env:  []string{"OPENCODE_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "opencode",
			Models: map[string]ModelInfo{
				"anthropic/claude-sonnet-4": {
					ID:          "anthropic/claude-sonnet-4",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"openai/gpt-4.1": {
					ID:          "openai/gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"zhipuai/glm-4.5-flash": {
					ID:          "zhipuai/glm-4.5-flash",
					Name:        "GLM-4.5-Flash",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  &[]float64{0}[0],
						CacheWrite: &[]float64{0}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
			},
		},
		"openrouter": {
			ID:   "openrouter",
			Env:  []string{"OPENROUTER_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "OpenRouter",
			Models: map[string]ModelInfo{
				"anthropic/claude-3.5-haiku": {
					ID:          "anthropic/claude-3.5-haiku",
					Name:        "Claude Haiku 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     4,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic/claude-3.7-sonnet": {
					ID:          "anthropic/claude-3.7-sonnet",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  128000,
					},
				},
				"anthropic/claude-4-sonnet-20250522": {
					ID:          "anthropic/claude-4-sonnet-20250522",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"anthropic/claude-opus-4": {
					ID:          "anthropic/claude-opus-4",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"anthropic/claude-opus-4.1": {
					ID:          "anthropic/claude-opus-4.1",
					Name:        "Claude Opus 4.1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"cognitivecomputations/dolphin3.0-mistral-24b": {
					ID:          "cognitivecomputations/dolphin3.0-mistral-24b",
					Name:        "Dolphin3.0 Mistral 24B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"cognitivecomputations/dolphin3.0-r1-mistral-24b": {
					ID:          "cognitivecomputations/dolphin3.0-r1-mistral-24b",
					Name:        "Dolphin3.0 R1 Mistral 24B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"deepseek/deepseek-chat-v3-0324": {
					ID:          "deepseek/deepseek-chat-v3-0324",
					Name:        "DeepSeek V3 0324",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16384,
						Output:  8192,
					},
				},
				"deepseek/deepseek-r1-0528-qwen3-8b:free": {
					ID:          "deepseek/deepseek-r1-0528-qwen3-8b:free",
					Name:        "Deepseek R1 0528 Qwen3 8B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"deepseek/deepseek-r1-0528:free": {
					ID:          "deepseek/deepseek-r1-0528:free",
					Name:        "R1 0528 (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"deepseek/deepseek-r1-distill-llama-70b": {
					ID:          "deepseek/deepseek-r1-distill-llama-70b",
					Name:        "DeepSeek R1 Distill Llama 70B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"deepseek/deepseek-r1-distill-qwen-14b": {
					ID:          "deepseek/deepseek-r1-distill-qwen-14b",
					Name:        "DeepSeek R1 Distill Qwen 14B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 64000,
						Output:  8192,
					},
				},
				"deepseek/deepseek-r1:free": {
					ID:          "deepseek/deepseek-r1:free",
					Name:        "R1 (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"deepseek/deepseek-v3-base:free": {
					ID:          "deepseek/deepseek-v3-base:free",
					Name:        "DeepSeek V3 Base (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"featherless/qwerky-72b": {
					ID:          "featherless/qwerky-72b",
					Name:        "Qwerky 72B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"google/gemini-2.0-flash-001": {
					ID:          "google/gemini-2.0-flash-001",
					Name:        "Gemini 2.0 Flash",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.025}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"google/gemini-2.0-flash-exp:free": {
					ID:          "google/gemini-2.0-flash-exp:free",
					Name:        "Gemini 2.0 Flash Experimental (free)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  1048576,
					},
				},
				"google/gemini-2.5-flash": {
					ID:          "google/gemini-2.5-flash",
					Name:        "Gemini 2.5 Flash",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     2.5,
						CacheRead:  &[]float64{0.0375}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"google/gemini-2.5-pro": {
					ID:          "google/gemini-2.5-pro",
					Name:        "Gemini 2.5 Pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"google/gemini-2.5-pro-preview-05-06": {
					ID:          "google/gemini-2.5-pro-preview-05-06",
					Name:        "Gemini 2.5 Pro Preview 05-06",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"google/gemini-2.5-pro-preview-06-05": {
					ID:          "google/gemini-2.5-pro-preview-06-05",
					Name:        "Gemini 2.5 Pro Preview 06-05",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"google/gemma-2-9b-it:free": {
					ID:          "google/gemma-2-9b-it:free",
					Name:        "Gemma 2 9B (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"google/gemma-3-12b-it": {
					ID:          "google/gemma-3-12b-it",
					Name:        "Gemma 3 12B IT",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 96000,
						Output:  8192,
					},
				},
				"google/gemma-3-27b-it": {
					ID:          "google/gemma-3-27b-it",
					Name:        "Gemma 3 27B IT",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 96000,
						Output:  8192,
					},
				},
				"google/gemma-3n-e4b-it": {
					ID:          "google/gemma-3n-e4b-it",
					Name:        "Gemma 3n E4B IT",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"google/gemma-3n-e4b-it:free": {
					ID:          "google/gemma-3n-e4b-it:free",
					Name:        "Gemma 3n 4B (free)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"meta-llama/llama-3.2-11b-vision-instruct": {
					ID:          "meta-llama/llama-3.2-11b-vision-instruct",
					Name:        "Llama 3.2 11B Vision Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"meta-llama/llama-3.3-70b-instruct:free": {
					ID:          "meta-llama/llama-3.3-70b-instruct:free",
					Name:        "Llama 3.3 70B Instruct (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  65536,
					},
				},
				"meta-llama/llama-4-scout:free": {
					ID:          "meta-llama/llama-4-scout:free",
					Name:        "Llama 4 Scout (free)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 64000,
						Output:  64000,
					},
				},
				"microsoft/mai-ds-r1:free": {
					ID:          "microsoft/mai-ds-r1:free",
					Name:        "MAI DS R1 (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"mistralai/codestral-2508": {
					ID:          "mistralai/codestral-2508",
					Name:        "Codestral 2508",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.9,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  256000,
					},
				},
				"mistralai/devstral-medium-2507": {
					ID:          "mistralai/devstral-medium-2507",
					Name:        "Devstral Medium",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"mistralai/devstral-small-2505": {
					ID:          "mistralai/devstral-small-2505",
					Name:        "Devstral Small",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.06,
						Output:     0.12,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistralai/devstral-small-2505:free": {
					ID:          "mistralai/devstral-small-2505:free",
					Name:        "Devstral Small 2505 (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"mistralai/devstral-small-2507": {
					ID:          "mistralai/devstral-small-2507",
					Name:        "Devstral Small 1.1",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"mistralai/mistral-7b-instruct:free": {
					ID:          "mistralai/mistral-7b-instruct:free",
					Name:        "Mistral 7B Instruct (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"mistralai/mistral-nemo:free": {
					ID:          "mistralai/mistral-nemo:free",
					Name:        "Mistral Nemo (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"mistralai/mistral-small-3.1-24b-instruct": {
					ID:          "mistralai/mistral-small-3.1-24b-instruct",
					Name:        "Mistral Small 3.1 24B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"mistralai/mistral-small-3.2-24b-instruct": {
					ID:          "mistralai/mistral-small-3.2-24b-instruct",
					Name:        "Mistral Small 3.2 24B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 96000,
						Output:  8192,
					},
				},
				"mistralai/mistral-small-3.2-24b-instruct:free": {
					ID:          "mistralai/mistral-small-3.2-24b-instruct:free",
					Name:        "Mistral Small 3.2 24B (free)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 96000,
						Output:  96000,
					},
				},
				"moonshotai/kimi-dev-72b:free": {
					ID:          "moonshotai/kimi-dev-72b:free",
					Name:        "Kimi Dev 72b (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"moonshotai/kimi-k2": {
					ID:          "moonshotai/kimi-k2",
					Name:        "Kimi K2",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.55,
						Output:     2.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"moonshotai/kimi-k2:free": {
					ID:          "moonshotai/kimi-k2:free",
					Name:        "Kimi K2 (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 66000,
						Output:  66000,
					},
				},
				"nousresearch/deephermes-3-llama-3-8b-preview": {
					ID:          "nousresearch/deephermes-3-llama-3-8b-preview",
					Name:        "DeepHermes 3 Llama 3 8B Preview",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"openai/gpt-4.1": {
					ID:          "openai/gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4.1-mini": {
					ID:          "openai/gpt-4.1-mini",
					Name:        "GPT-4.1 Mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     1.6,
						CacheRead:  &[]float64{0.1}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4o-mini": {
					ID:          "openai/gpt-4o-mini",
					Name:        "GPT-4o-mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-5": {
					ID:          "openai/gpt-5",
					Name:        "GPT-5",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"openai/gpt-5-mini": {
					ID:          "openai/gpt-5-mini",
					Name:        "GPT-5 Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"openai/gpt-5-nano": {
					ID:          "openai/gpt-5-nano",
					Name:        "GPT-5 Nano",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.05,
						Output:     0.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"openai/gpt-oss-120b": {
					ID:          "openai/gpt-oss-120b",
					Name:        "GPT OSS 120B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"openai/gpt-oss-20b": {
					ID:          "openai/gpt-oss-20b",
					Name:        "GPT OSS 20B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.05,
						Output:     0.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
				"openai/o4-mini": {
					ID:          "openai/o4-mini",
					Name:        "o4 Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.28}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openrouter/cypher-alpha:free": {
					ID:          "openrouter/cypher-alpha:free",
					Name:        "Cypher Alpha (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1000000,
						Output:  1000000,
					},
				},
				"openrouter/horizon-alpha": {
					ID:          "openrouter/horizon-alpha",
					Name:        "Horizon Alpha",
					Attachment:  true,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  128000,
					},
				},
				"openrouter/horizon-beta": {
					ID:          "openrouter/horizon-beta",
					Name:        "Horizon Beta",
					Attachment:  true,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  128000,
					},
				},
				"qwen/qwen-2.5-coder-32b-instruct": {
					ID:          "qwen/qwen-2.5-coder-32b-instruct",
					Name:        "Qwen2.5 Coder 32B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"qwen/qwen2.5-vl-32b-instruct:free": {
					ID:          "qwen/qwen2.5-vl-32b-instruct:free",
					Name:        "Qwen2.5 VL 32B Instruct (free)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 8192,
						Output:  8192,
					},
				},
				"qwen/qwen2.5-vl-72b-instruct": {
					ID:          "qwen/qwen2.5-vl-72b-instruct",
					Name:        "Qwen2.5 VL 72B Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"qwen/qwen2.5-vl-72b-instruct:free": {
					ID:          "qwen/qwen2.5-vl-72b-instruct:free",
					Name:        "Qwen2.5 VL 72B Instruct (free)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"qwen/qwen3-14b:free": {
					ID:          "qwen/qwen3-14b:free",
					Name:        "Qwen3 14B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 40960,
						Output:  40960,
					},
				},
				"qwen/qwen3-235b-a22b-07-25": {
					ID:          "qwen/qwen3-235b-a22b-07-25",
					Name:        "Qwen3 235B A22B Instruct 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.85,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"qwen/qwen3-235b-a22b-07-25:free": {
					ID:          "qwen/qwen3-235b-a22b-07-25:free",
					Name:        "Qwen3 235B A22B Instruct 2507 (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"qwen/qwen3-235b-a22b:free": {
					ID:          "qwen/qwen3-235b-a22b:free",
					Name:        "Qwen3 235B A22B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  131072,
					},
				},
				"qwen/qwen3-30b-a3b-instruct-2507": {
					ID:          "qwen/qwen3-30b-a3b-instruct-2507",
					Name:        "Qwen3 30B A3B Instruct 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     0.8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  33000,
					},
				},
				"qwen/qwen3-30b-a3b:free": {
					ID:          "qwen/qwen3-30b-a3b:free",
					Name:        "Qwen3 30B A3B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 40960,
						Output:  40960,
					},
				},
				"qwen/qwen3-32b:free": {
					ID:          "qwen/qwen3-32b:free",
					Name:        "Qwen3 32B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 40960,
						Output:  40960,
					},
				},
				"qwen/qwen3-8b:free": {
					ID:          "qwen/qwen3-8b:free",
					Name:        "Qwen3 8B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 40960,
						Output:  40960,
					},
				},
				"qwen/qwen3-coder": {
					ID:          "qwen/qwen3-coder",
					Name:        "Qwen3 Coder",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     1.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"qwen/qwen3-coder:free": {
					ID:          "qwen/qwen3-coder:free",
					Name:        "Qwen3 Coder 480B A35B Instruct (free)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"qwen/qwq-32b:free": {
					ID:          "qwen/qwq-32b:free",
					Name:        "QwQ 32B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"rekaai/reka-flash-3": {
					ID:          "rekaai/reka-flash-3",
					Name:        "Reka Flash 3",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"sarvamai/sarvam-m:free": {
					ID:          "sarvamai/sarvam-m:free",
					Name:        "Sarvam-M (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"thudm/glm-z1-32b:free": {
					ID:          "thudm/glm-z1-32b:free",
					Name:        "GLM Z1 32B (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  32768,
					},
				},
				"tngtech/deepseek-r1t2-chimera:free": {
					ID:          "tngtech/deepseek-r1t2-chimera:free",
					Name:        "DeepSeek R1T2 Chimera (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163840,
						Output:  163840,
					},
				},
				"x-ai/grok-3": {
					ID:          "x-ai/grok-3",
					Name:        "Grok 3",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"x-ai/grok-3-beta": {
					ID:          "x-ai/grok-3-beta",
					Name:        "Grok 3 Beta",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"x-ai/grok-3-mini": {
					ID:          "x-ai/grok-3-mini",
					Name:        "Grok 3 Mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: &[]float64{0.5}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"x-ai/grok-3-mini-beta": {
					ID:          "x-ai/grok-3-mini-beta",
					Name:        "Grok 3 Mini Beta",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: &[]float64{0.5}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"x-ai/grok-4": {
					ID:          "x-ai/grok-4",
					Name:        "Grok 4",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 256000,
						Output:  64000,
					},
				},
				"z-ai/glm-4.5": {
					ID:          "z-ai/glm-4.5",
					Name:        "GLM 4.5",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     2.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  96000,
					},
				},
				"z-ai/glm-4.5-air": {
					ID:          "z-ai/glm-4.5-air",
					Name:        "GLM 4.5 Air",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     1.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  96000,
					},
				},
				"z-ai/glm-4.5-air:free": {
					ID:          "z-ai/glm-4.5-air:free",
					Name:        "GLM 4.5 Air (free)",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  96000,
					},
				},
			},
		},
		"requesty": {
			ID:   "requesty",
			Env:  []string{"REQUESTY_API_KEY"},
			NPM:  "@requesty/ai-sdk",
			Name: "Requesty",
			Models: map[string]ModelInfo{
				"anthropic/claude-3-7-sonnet": {
					ID:          "anthropic/claude-3-7-sonnet",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"anthropic/claude-4-sonnet-20250522": {
					ID:          "anthropic/claude-4-sonnet-20250522",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"anthropic/claude-opus-4": {
					ID:          "anthropic/claude-opus-4",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"anthropic/claude-opus-4-1-20250805": {
					ID:          "anthropic/claude-opus-4-1-20250805",
					Name:        "Claude Opus 4.1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"google/gemini-2.5-flash": {
					ID:          "google/gemini-2.5-flash",
					Name:        "Gemini 2.5 Flash",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     2.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: &[]float64{0.55}[0],
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"google/gemini-2.5-pro": {
					ID:          "google/gemini-2.5-pro",
					Name:        "Gemini 2.5 Pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: &[]float64{2.375}[0],
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"openai/gpt-4.1": {
					ID:          "openai/gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4.1-mini": {
					ID:          "openai/gpt-4.1-mini",
					Name:        "GPT-4.1 Mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     1.6,
						CacheRead:  &[]float64{0.1}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4o-mini": {
					ID:          "openai/gpt-4o-mini",
					Name:        "GPT-4o Mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/o4-mini": {
					ID:          "openai/o4-mini",
					Name:        "o4 Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.28}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
			},
		},
		"togetherai": {
			ID:   "togetherai",
			Env:  []string{"TOGETHER_API_KEY"},
			NPM:  "@ai-sdk/togetherai",
			Name: "Together AI",
			Models: map[string]ModelInfo{
				"Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8",
					Name:        "Qwen3 Coder 480B A35B Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"deepseek-ai/DeepSeek-R1": {
					ID:          "deepseek-ai/DeepSeek-R1",
					Name:        "DeepSeek R1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     7,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 163839,
						Output:  12288,
					},
				},
				"deepseek-ai/DeepSeek-V3": {
					ID:          "deepseek-ai/DeepSeek-V3",
					Name:        "DeepSeek V3",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     1.25,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  12288,
					},
				},
				"meta-llama/Llama-3.3-70B-Instruct-Turbo": {
					ID:          "meta-llama/Llama-3.3-70B-Instruct-Turbo",
					Name:        "Llama 3.3 70B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.88,
						Output:     0.88,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  66536,
					},
				},
				"moonshotai/Kimi-K2-Instruct": {
					ID:          "moonshotai/Kimi-K2-Instruct",
					Name:        "Kimi K2 Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  32768,
					},
				},
			},
		},
		"upstage": {
			ID:   "upstage",
			Env:  []string{"UPSTAGE_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Upstage",
			Models: map[string]ModelInfo{
				"solar-mini": {
					ID:          "solar-mini",
					Name:        "solar-mini",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  4096,
					},
				},
				"solar-pro2": {
					ID:          "solar-pro2",
					Name:        "solar-pro2",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     0.25,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
			},
		},
		"v0": {
			ID:   "v0",
			Env:  []string{"V0_API_KEY"},
			NPM:  "@ai-sdk/vercel",
			Name: "v0",
			Models: map[string]ModelInfo{
				"v0-1.0-md": {
					ID:          "v0-1.0-md",
					Name:        "v0-1.0-md",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32000,
					},
				},
				"v0-1.5-lg": {
					ID:          "v0-1.5-lg",
					Name:        "v0-1.5-lg",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 512000,
						Output:  32000,
					},
				},
				"v0-1.5-md": {
					ID:          "v0-1.5-md",
					Name:        "v0-1.5-md",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32000,
					},
				},
			},
		},
		"venice": {
			ID:   "venice",
			Env:  []string{"VENICE_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Venice AI",
			Models: map[string]ModelInfo{
				"deepseek-coder-v2-lite": {
					ID:          "deepseek-coder-v2-lite",
					Name:        "DeepSeek Coder V2 Lite",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"deepseek-r1-671b": {
					ID:          "deepseek-r1-671b",
					Name:        "DeepSeek R1 671B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3.5,
						Output:     14,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"dolphin-2.9.2-qwen2-72b": {
					ID:          "dolphin-2.9.2-qwen2-72b",
					Name:        "Dolphin 72B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.7,
						Output:     2.8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"llama-3.1-405b": {
					ID:          "llama-3.1-405b",
					Name:        "Llama 3.1 405B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.5,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
				"llama-3.2-3b": {
					ID:          "llama-3.2-3b",
					Name:        "Llama 3.2 3B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"llama-3.3-70b": {
					ID:          "llama-3.3-70b",
					Name:        "Llama 3.3 70B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.7,
						Output:     2.8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 65536,
						Output:  8192,
					},
				},
				"mistral-31-24b": {
					ID:          "mistral-31-24b",
					Name:        "Venice Medium",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"qwen-2.5-coder-32b": {
					ID:          "qwen-2.5-coder-32b",
					Name:        "Qwen 2.5 Coder 32B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"qwen-2.5-qwq-32b": {
					ID:          "qwen-2.5-qwq-32b",
					Name:        "Venice Reasoning",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"qwen-2.5-vl": {
					ID:          "qwen-2.5-vl",
					Name:        "Qwen 2.5 VL 72B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.7,
						Output:     2.8,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"qwen3-235b": {
					ID:          "qwen3-235b",
					Name:        "Venice Large",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.5,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"qwen3-4b": {
					ID:          "qwen3-4b",
					Name:        "Venice Small",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
				"venice-uncensored": {
					ID:          "venice-uncensored",
					Name:        "Venice Uncensored 1.1",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32768,
						Output:  8192,
					},
				},
			},
		},
		"vercel": {
			ID:   "vercel",
			Env:  []string{"AI_GATEWAY_API_KEY"},
			NPM:  "@ai-sdk/gateway",
			Name: "Vercel AI Gateway",
			Models: map[string]ModelInfo{
				"amazon/nova-lite": {
					ID:          "amazon/nova-lite",
					Name:        "Nova Lite",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.06,
						Output:     0.24,
						CacheRead:  &[]float64{0.015}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 300000,
						Output:  8192,
					},
				},
				"amazon/nova-micro": {
					ID:          "amazon/nova-micro",
					Name:        "Nova Micro",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.035,
						Output:     0.14,
						CacheRead:  &[]float64{0.00875}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  8192,
					},
				},
				"amazon/nova-pro": {
					ID:          "amazon/nova-pro",
					Name:        "Nova Pro",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     3.2,
						CacheRead:  &[]float64{0.2}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 300000,
						Output:  8192,
					},
				},
				"anthropic/claude-3-5-haiku": {
					ID:          "anthropic/claude-3-5-haiku",
					Name:        "Claude Haiku 3.5",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.8,
						Output:     4,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: &[]float64{1}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic/claude-3-haiku": {
					ID:          "anthropic/claude-3-haiku",
					Name:        "Claude Haiku 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.25,
						Output:     1.25,
						CacheRead:  &[]float64{0.03}[0],
						CacheWrite: &[]float64{0.3}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"anthropic/claude-3-opus": {
					ID:          "anthropic/claude-3-opus",
					Name:        "Claude Opus 3",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  4096,
					},
				},
				"anthropic/claude-3.5-sonnet": {
					ID:          "anthropic/claude-3.5-sonnet",
					Name:        "Claude Sonnet 3.5 v2",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  8192,
					},
				},
				"anthropic/claude-3.7-sonnet": {
					ID:          "anthropic/claude-3.7-sonnet",
					Name:        "Claude Sonnet 3.7",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"anthropic/claude-4-1-opus": {
					ID:          "anthropic/claude-4-1-opus",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"anthropic/claude-4-opus": {
					ID:          "anthropic/claude-4-opus",
					Name:        "Claude Opus 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      15,
						Output:     75,
						CacheRead:  &[]float64{1.5}[0],
						CacheWrite: &[]float64{18.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  32000,
					},
				},
				"anthropic/claude-4-sonnet": {
					ID:          "anthropic/claude-4-sonnet",
					Name:        "Claude Sonnet 4",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.3}[0],
						CacheWrite: &[]float64{3.75}[0],
					},
					Limit: Limit{
						Context: 200000,
						Output:  64000,
					},
				},
				"cerebras/qwen3-coder": {
					ID:          "cerebras/qwen3-coder",
					Name:        "Qwen 3 Coder 480B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131000,
						Output:  32000,
					},
				},
				"deepseek/deepseek-r1": {
					ID:          "deepseek/deepseek-r1",
					Name:        "DeepSeek-R1",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.35,
						Output:     5.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"deepseek/deepseek-r1-distill-llama-70b": {
					ID:          "deepseek/deepseek-r1-distill-llama-70b",
					Name:        "DeepSeek R1 Distill Llama 70B",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.75,
						Output:     0.99,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"google/gemini-2.0-flash": {
					ID:          "google/gemini-2.0-flash",
					Name:        "Gemini 2.0 Flash",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.025}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"google/gemini-2.0-flash-lite": {
					ID:          "google/gemini-2.0-flash-lite",
					Name:        "Gemini 2.0 Flash Lite",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.075,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  8192,
					},
				},
				"google/gemini-2.5-flash": {
					ID:          "google/gemini-2.5-flash",
					Name:        "Gemini 2.5 Flash",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     2.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"google/gemini-2.5-pro": {
					ID:          "google/gemini-2.5-pro",
					Name:        "Gemini 2.5 Pro",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  &[]float64{0.31}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1048576,
						Output:  65536,
					},
				},
				"meta/llama-3.3-70b": {
					ID:          "meta/llama-3.3-70b",
					Name:        "Llama-3.3-70B-Instruct",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta/llama-4-maverick": {
					ID:          "meta/llama-4-maverick",
					Name:        "Llama-4-Maverick-17B-128E-Instruct-FP8",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"meta/llama-4-scout": {
					ID:          "meta/llama-4-scout",
					Name:        "Llama-4-Scout-17B-16E-Instruct-FP8",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"mistral/codestral": {
					ID:          "mistral/codestral",
					Name:        "Codestral",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.9,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 256000,
						Output:  4096,
					},
				},
				"mistral/magistral-medium": {
					ID:          "mistral/magistral-medium",
					Name:        "Magistral Medium",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"mistral/magistral-small": {
					ID:          "mistral/magistral-small",
					Name:        "Magistral Small",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.5,
						Output:     1.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistral/ministral-3b": {
					ID:          "mistral/ministral-3b",
					Name:        "Ministral 3B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.04,
						Output:     0.04,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistral/ministral-8b": {
					ID:          "mistral/ministral-8b",
					Name:        "Ministral 8B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistral/mistral-large": {
					ID:          "mistral/mistral-large",
					Name:        "Mistral Large",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
				"mistral/mistral-small": {
					ID:          "mistral/mistral-small",
					Name:        "Mistral Small",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"mistral/mixtral-8x22b-instruct": {
					ID:          "mistral/mixtral-8x22b-instruct",
					Name:        "Mixtral 8x22B",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 64000,
						Output:  64000,
					},
				},
				"mistral/pixtral-12b": {
					ID:          "mistral/pixtral-12b",
					Name:        "Pixtral 12B",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"mistral/pixtral-large": {
					ID:          "mistral/pixtral-large",
					Name:        "Pixtral Large",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     6,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  128000,
					},
				},
				"moonshotai/kimi-k2": {
					ID:          "moonshotai/kimi-k2",
					Name:        "Kimi K2 Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     3,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 131072,
						Output:  16384,
					},
				},
				"morph/morph-v3-fast": {
					ID:          "morph/morph-v3-fast",
					Name:        "Morph v3 Fast",
					Attachment:  false,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0.8,
						Output:     1.2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 16000,
						Output:  16000,
					},
				},
				"morph/morph-v3-large": {
					ID:          "morph/morph-v3-large",
					Name:        "Morph v3 Large",
					Attachment:  false,
					Reasoning:   false,
					Temperature: false,
					Cost: Cost{
						Input:      0.9,
						Output:     1.9,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 32000,
						Output:  32000,
					},
				},
				"openai/gpt-4-turbo": {
					ID:          "openai/gpt-4-turbo",
					Name:        "GPT-4 Turbo",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      10,
						Output:     30,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"openai/gpt-4.1": {
					ID:          "openai/gpt-4.1",
					Name:        "GPT-4.1",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4.1-mini": {
					ID:          "openai/gpt-4.1-mini",
					Name:        "GPT-4.1 mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.4,
						Output:     1.6,
						CacheRead:  &[]float64{0.1}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4.1-nano": {
					ID:          "openai/gpt-4.1-nano",
					Name:        "GPT-4.1 nano",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.4,
						CacheRead:  &[]float64{0.03}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 1047576,
						Output:  32768,
					},
				},
				"openai/gpt-4o": {
					ID:          "openai/gpt-4o",
					Name:        "GPT-4o",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2.5,
						Output:     10,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-4o-mini": {
					ID:          "openai/gpt-4o-mini",
					Name:        "GPT-4o mini",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.15,
						Output:     0.6,
						CacheRead:  &[]float64{0.08}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
				"openai/gpt-5": {
					ID:          "openai/gpt-5",
					Name:        "GPT-5",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.25,
						Output:     10,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"openai/gpt-5-mini": {
					ID:          "openai/gpt-5-mini",
					Name:        "GPT-5 Mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0.25,
						Output:     2,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"openai/gpt-5-nano": {
					ID:          "openai/gpt-5-nano",
					Name:        "GPT-5 Nano",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      0.05,
						Output:     0.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 400000,
						Output:  128000,
					},
				},
				"openai/o1": {
					ID:          "openai/o1",
					Name:        "o1",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      15,
						Output:     60,
						CacheRead:  &[]float64{7.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openai/o3": {
					ID:          "openai/o3",
					Name:        "o3",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      2,
						Output:     8,
						CacheRead:  &[]float64{0.5}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openai/o3-mini": {
					ID:          "openai/o3-mini",
					Name:        "o3-mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.55}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"openai/o4-mini": {
					ID:          "openai/o4-mini",
					Name:        "o4-mini",
					Attachment:  true,
					Reasoning:   true,
					Temperature: false,
					Cost: Cost{
						Input:      1.1,
						Output:     4.4,
						CacheRead:  &[]float64{0.28}[0],
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 200000,
						Output:  100000,
					},
				},
				"vercel/v0-1.0-md": {
					ID:          "vercel/v0-1.0-md",
					Name:        "v0-1.0-md",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32000,
					},
				},
				"vercel/v0-1.5-md": {
					ID:          "vercel/v0-1.5-md",
					Name:        "v0-1.5-md",
					Attachment:  true,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32000,
					},
				},
				"xai/grok-2": {
					ID:          "xai/grok-2",
					Name:        "Grok 2",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"xai/grok-2-vision": {
					ID:          "xai/grok-2-vision",
					Name:        "Grok 2 Vision",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 8192,
						Output:  4096,
					},
				},
				"xai/grok-3": {
					ID:          "xai/grok-3",
					Name:        "Grok 3",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"xai/grok-3-fast": {
					ID:          "xai/grok-3-fast",
					Name:        "Grok 3 Fast",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      5,
						Output:     25,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: &[]float64{25}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"xai/grok-3-mini": {
					ID:          "xai/grok-3-mini",
					Name:        "Grok 3 Mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: &[]float64{0.5}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"xai/grok-3-mini-fast": {
					ID:          "xai/grok-3-mini-fast",
					Name:        "Grok 3 Mini Fast",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     4,
						CacheRead:  &[]float64{0.15}[0],
						CacheWrite: &[]float64{4}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"xai/grok-4": {
					ID:          "xai/grok-4",
					Name:        "Grok 4",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 256000,
						Output:  64000,
					},
				},
			},
		},
		"wandb": {
			ID:   "wandb",
			Env:  []string{"WANDB_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Weights & Biases",
			Models: map[string]ModelInfo{
				"Qwen/Qwen3-235B-A22B-Instruct-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Instruct-2507",
					Name:        "Qwen3 235B A22B Instruct 2507",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"Qwen/Qwen3-235B-A22B-Thinking-2507": {
					ID:          "Qwen/Qwen3-235B-A22B-Thinking-2507",
					Name:        "Qwen3-235B-A22B-Thinking-2507",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.1,
						Output:     0.1,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  131072,
					},
				},
				"Qwen/Qwen3-Coder-480B-A35B-Instruct": {
					ID:          "Qwen/Qwen3-Coder-480B-A35B-Instruct",
					Name:        "Qwen3-Coder-480B-A35B-Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1,
						Output:     1.5,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 262144,
						Output:  66536,
					},
				},
				"deepseek-ai/DeepSeek-R1-0528": {
					ID:          "deepseek-ai/DeepSeek-R1-0528",
					Name:        "DeepSeek-R1-0528",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      1.35,
						Output:     5.4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 161000,
						Output:  163840,
					},
				},
				"deepseek-ai/DeepSeek-V3-0324": {
					ID:          "deepseek-ai/DeepSeek-V3-0324",
					Name:        "DeepSeek-V3-0324",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.14,
						Output:     2.75,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 161000,
						Output:  8192,
					},
				},
				"meta-llama/Llama-3.1-8B-Instruct": {
					ID:          "meta-llama/Llama-3.1-8B-Instruct",
					Name:        "Meta-Llama-3.1-8B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.22,
						Output:     0.22,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"meta-llama/Llama-3.3-70B-Instruct": {
					ID:          "meta-llama/Llama-3.3-70B-Instruct",
					Name:        "Llama-3.3-70B-Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.71,
						Output:     0.71,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  32768,
					},
				},
				"meta-llama/Llama-4-Scout-17B-16E-Instruct": {
					ID:          "meta-llama/Llama-4-Scout-17B-16E-Instruct",
					Name:        "Llama 4 Scout 17B 16E Instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.17,
						Output:     0.66,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 64000,
						Output:  8192,
					},
				},
				"microsoft/Phi-4-mini-instruct": {
					ID:          "microsoft/Phi-4-mini-instruct",
					Name:        "Phi-4-mini-instruct",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.08,
						Output:     0.35,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  4096,
					},
				},
				"moonshotai/Kimi-K2-Instruct": {
					ID:          "moonshotai/Kimi-K2-Instruct",
					Name:        "Kimi-K2-Instruct",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      1.35,
						Output:     4,
						CacheRead:  nil,
						CacheWrite: nil,
					},
					Limit: Limit{
						Context: 128000,
						Output:  16384,
					},
				},
			},
		},
		"xai": {
			ID:   "xai",
			Env:  []string{"XAI_API_KEY"},
			NPM:  "@ai-sdk/xai",
			Name: "xAI",
			Models: map[string]ModelInfo{
				"grok-2": {
					ID:          "grok-2",
					Name:        "Grok 2",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-2-1212": {
					ID:          "grok-2-1212",
					Name:        "Grok 2 (1212)",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-2-latest": {
					ID:          "grok-2-latest",
					Name:        "Grok 2 Latest",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-2-vision": {
					ID:          "grok-2-vision",
					Name:        "Grok 2 Vision",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 8192,
						Output:  4096,
					},
				},
				"grok-2-vision-1212": {
					ID:          "grok-2-vision-1212",
					Name:        "Grok 2 Vision (1212)",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 8192,
						Output:  4096,
					},
				},
				"grok-2-vision-latest": {
					ID:          "grok-2-vision-latest",
					Name:        "Grok 2 Vision Latest",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      2,
						Output:     10,
						CacheRead:  &[]float64{2}[0],
						CacheWrite: &[]float64{10}[0],
					},
					Limit: Limit{
						Context: 8192,
						Output:  4096,
					},
				},
				"grok-3": {
					ID:          "grok-3",
					Name:        "Grok 3",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-fast": {
					ID:          "grok-3-fast",
					Name:        "Grok 3 Fast",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      5,
						Output:     25,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: &[]float64{25}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-fast-latest": {
					ID:          "grok-3-fast-latest",
					Name:        "Grok 3 Fast Latest",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      5,
						Output:     25,
						CacheRead:  &[]float64{1.25}[0],
						CacheWrite: &[]float64{25}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-latest": {
					ID:          "grok-3-latest",
					Name:        "Grok 3 Latest",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-mini": {
					ID:          "grok-3-mini",
					Name:        "Grok 3 Mini",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: &[]float64{0.5}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-mini-fast": {
					ID:          "grok-3-mini-fast",
					Name:        "Grok 3 Mini Fast",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     4,
						CacheRead:  &[]float64{0.15}[0],
						CacheWrite: &[]float64{4}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-mini-fast-latest": {
					ID:          "grok-3-mini-fast-latest",
					Name:        "Grok 3 Mini Fast Latest",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     4,
						CacheRead:  &[]float64{0.15}[0],
						CacheWrite: &[]float64{4}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-3-mini-latest": {
					ID:          "grok-3-mini-latest",
					Name:        "Grok 3 Mini Latest",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.3,
						Output:     0.5,
						CacheRead:  &[]float64{0.075}[0],
						CacheWrite: &[]float64{0.5}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  8192,
					},
				},
				"grok-4": {
					ID:          "grok-4",
					Name:        "Grok 4",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      3,
						Output:     15,
						CacheRead:  &[]float64{0.75}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 256000,
						Output:  64000,
					},
				},
				"grok-beta": {
					ID:          "grok-beta",
					Name:        "Grok Beta",
					Attachment:  false,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      5,
						Output:     15,
						CacheRead:  &[]float64{5}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  4096,
					},
				},
				"grok-vision-beta": {
					ID:          "grok-vision-beta",
					Name:        "Grok Vision Beta",
					Attachment:  true,
					Reasoning:   false,
					Temperature: true,
					Cost: Cost{
						Input:      5,
						Output:     15,
						CacheRead:  &[]float64{5}[0],
						CacheWrite: &[]float64{15}[0],
					},
					Limit: Limit{
						Context: 8192,
						Output:  4096,
					},
				},
			},
		},
		"zhipuai": {
			ID:   "zhipuai",
			Env:  []string{"ZHIPU_API_KEY"},
			NPM:  "@ai-sdk/openai-compatible",
			Name: "Zhipu AI",
			Models: map[string]ModelInfo{
				"glm-4.5": {
					ID:          "glm-4.5",
					Name:        "GLM-4.5",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.6,
						Output:     2.2,
						CacheRead:  &[]float64{0.11}[0],
						CacheWrite: &[]float64{0}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
				"glm-4.5-air": {
					ID:          "glm-4.5-air",
					Name:        "GLM-4.5-Air",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0.2,
						Output:     1.1,
						CacheRead:  &[]float64{0.03}[0],
						CacheWrite: &[]float64{0}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
				"glm-4.5-flash": {
					ID:          "glm-4.5-flash",
					Name:        "GLM-4.5-Flash",
					Attachment:  false,
					Reasoning:   true,
					Temperature: true,
					Cost: Cost{
						Input:      0,
						Output:     0,
						CacheRead:  &[]float64{0}[0],
						CacheWrite: &[]float64{0}[0],
					},
					Limit: Limit{
						Context: 131072,
						Output:  98304,
					},
				},
			},
		},
	}
}
